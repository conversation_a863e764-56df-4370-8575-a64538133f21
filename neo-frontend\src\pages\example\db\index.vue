<template>
  <div class="phone-info-management-container">
    <!-- 筛选表单 -->
    <t-card class="filter-card" :bordered="false">
      <t-form :data="searchFormState" :label-width="80" colon @reset="onReset" @submit="onSubmit">
        <t-row>
          <t-col :span="10">
            <t-row :gutter="[24, 24]">
              <t-col :span="4">
                <t-form-item label="手机品牌" name="brand">
                  <t-input
                    v-model="searchFormState.brand"
                    class="form-item-content"
                    type="search"
                    placeholder="请输入手机品牌"
                    clearable
                  />
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="手机型号" name="model">
                  <t-input
                    v-model="searchFormState.model"
                    class="form-item-content"
                    placeholder="请输入手机型号"
                    clearable
                  />
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="操作系统" name="osType">
                  <t-select
                    v-model="searchFormState.osType"
                    class="form-item-content"
                    :options="osTypeOptions"
                    placeholder="请选择操作系统"
                    clearable
                  />
                </t-form-item>
              </t-col>
            </t-row>
            <t-row :gutter="[24, 24]">
              <t-col :span="4">
                <t-form-item label="网络类型" name="networkType">
                  <t-select
                    v-model="searchFormState.networkType"
                    class="form-item-content"
                    :options="networkTypeOptions"
                    placeholder="请选择网络类型"
                    clearable
                  />
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="5G支持" name="is5gSupported">
                  <t-select
                    v-model="searchFormState.is5gSupported"
                    class="form-item-content"
                    :options="supportOptions"
                    placeholder="请选择5G支持"
                    clearable
                  />
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="颜色" name="color">
                  <t-input
                    v-model="searchFormState.color"
                    class="form-item-content"
                    placeholder="请输入颜色"
                    clearable
                  />
                </t-form-item>
              </t-col>
            </t-row>
          </t-col>

          <t-col :span="2" class="operation-container">
            <t-button theme="primary" type="submit" :style="{ marginLeft: 'var(--td-comp-margin-s)' }">
              查询
            </t-button>
            <t-button type="reset" variant="base" theme="default">
              重置
            </t-button>
          </t-col>
        </t-row>
      </t-form>
    </t-card>

    <!-- 表格容器 -->
    <t-card class="table-card" :bordered="false">
      <div class="table-header">
        <div class="left-operation-container">
          <t-button @click="handleAdd">新增手机信息</t-button>
        </div>
      </div>

      <t-table
        :data="paginationData.dataSource.value"
        :columns="columns"
        :row-key="rowKey"
        vertical-align="top"
        :hover="true"
        :pagination="paginationData.tableConfig.value"
        :loading="paginationData.loading.value"
        @page-change="(pageInfo: any) => paginationData.handlePageChange(pageInfo, loadPhoneInfoData, searchFormState)"
      >
        <template #price="{ row }">
          <span>¥{{ row.price?.toLocaleString() || '-' }}</span>
        </template>
        
        <template #is5gSupported="{ row }">
          <t-tag v-if="row.is5gSupported === 'Y'" theme="success" variant="light">
            支持
          </t-tag>
          <t-tag v-else theme="default" variant="light">
            不支持
          </t-tag>
        </template>
        
        <template #op="slotProps">
          <t-space>
            <t-link theme="primary" @click="handleEdit(slotProps.row)">编辑</t-link>
            <t-popconfirm 
              content="确定要删除吗？" 
              @confirm="handleDelete(slotProps.row)"
            >
              <t-link theme="danger">删除</t-link>
            </t-popconfirm>
          </t-space>
        </template>
      </t-table>
    </t-card>

    <!-- 手机信息编辑弹窗 -->
    <EditModel 
      v-model:visible="formVisible" 
      :is-edit="isEdit" 
      :phone-data="editPhoneData"
      @success="onFormSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue';
  import {
    getPhoneInfoPage,
    deletePhoneInfo,
    getPhoneInfoById,
  } from '@/api/phoneInfo';
  import { PhoneInfo, PhoneInfoPageParams } from '@/api/model/phoneInfoModel';
  import { usePagination, CommonPageParams, CommonPageResult } from '@/hooks';
  import EditModel from './components/EditModel.vue';

  // 选项配置
  const osTypeOptions = [
    { value: 'Android', label: 'Android' },
    { value: 'iOS', label: 'iOS' },
    { value: 'HarmonyOS', label: 'HarmonyOS' },
  ];

  const networkTypeOptions = [
    { value: '4G', label: '4G' },
    { value: '5G', label: '5G' },
    { value: '4G/5G', label: '4G/5G' },
  ];

  const supportOptions = [
    { value: 'Y', label: '支持' },
    { value: 'N', label: '不支持' },
  ];

  // 定义表格列
  const columns = [
    {
      title: '品牌',
      colKey: 'brand',
      width: 100,
    },
    {
      title: '型号',
      colKey: 'model',
      width: 150,
    },
    {
      title: '处理器',
      colKey: 'cpu',
      width: 120,
    },
    {
      title: '内存(GB)',
      colKey: 'memorySize',
      width: 80,
    },
    {
      title: '存储(GB)',
      colKey: 'storageSize',
      width: 80,
    },
    {
      title: '屏幕尺寸',
      colKey: 'screenSize',
      width: 80,
    },
    {
      title: '操作系统',
      colKey: 'osType',
      width: 80,
    },
    {
      title: '价格',
      colKey: 'price',
      width: 100,
    },
    {
      title: '5G支持',
      colKey: 'is5gSupported',
      width: 80,
    },
    {
      title: '操作',
      colKey: 'op',
      width: 150,
    },
  ];

  // 使用通用分页 hook
  const paginationData = usePagination<PhoneInfo>({
    defaultCurrent: 1,
    defaultPageSize: 10,
  });

  // 搜索表单
  const searchFormState = reactive<Omit<PhoneInfoPageParams, 'current' | 'pageSize'>>({
    brand: '',
    model: '',
    osType: '',
    networkType: '',
    color: '',
    is5gSupported: '',
  });

  // 对话框相关
  const formVisible = ref(false);
  const isEdit = ref(false);
  const editPhoneData = ref<PhoneInfo>();
  const loading = ref(false);

  // 数据加载函数
  const loadPhoneInfoData = async (params: CommonPageParams): Promise<CommonPageResult<PhoneInfo>> => {
    const res = await getPhoneInfoPage(params as PhoneInfoPageParams);
    return {
      records: res.records,
      total: res.total,
      current: res.current,
      size: params.pageSize || paginationData.tableConfig.value?.pageSize || 10,
    };
  };

  // 查询
  const searchQuery = () => {
    paginationData.resetToFirstPage(loadPhoneInfoData, searchFormState);
  };

  // 表单提交
  const onSubmit = () => {
    searchQuery();
  };

  // 表单重置
  const onReset = () => {
    Object.keys(searchFormState).forEach(key => {
      (searchFormState as any)[key] = '';
    });
    searchQuery();
  };

  const rowKey = 'id';

  // 新增
  const handleAdd = () => {
    isEdit.value = false;
    editPhoneData.value = undefined;
    formVisible.value = true;
  };

  // 编辑
  const handleEdit = async (record: PhoneInfo) => {
    if (!record.id) {
      console.error('手机信息ID不存在');
      return;
    }
    
    loading.value = true;
    try {
      // 从后端获取完整的手机信息
      const phoneInfo = await getPhoneInfoById(record.id);
      isEdit.value = true;
      editPhoneData.value = phoneInfo;
      formVisible.value = true;
    } catch (error) {
      console.error('获取手机信息失败:', error);
    } finally {
      loading.value = false;
    }
  };

  // 删除
  const handleDelete = async (record: PhoneInfo) => {
    try {
      await deletePhoneInfo([record.id || '']);
      searchQuery();
    } catch (error) {
      console.error('删除失败:', error);
    }
  };

  // 表单操作成功回调
  const onFormSuccess = () => {
    paginationData.refreshData(loadPhoneInfoData, searchFormState);
  };

  onMounted(() => {
    paginationData.loadData(paginationData.buildPageParams(searchFormState), loadPhoneInfoData);
  });
</script>

<style lang="less" scoped>
.phone-info-management-container {
  .filter-card {
    margin-bottom: 16px;
  }

  .table-card {
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }
  }

  .operation-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .form-item-content {
    width: 100%;
  }
}
</style>
