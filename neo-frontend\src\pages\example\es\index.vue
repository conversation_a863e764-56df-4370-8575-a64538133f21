<template>
  <div class="es-example-container">
    <t-card :bordered="false">
      <h2>ES搜索示例</h2>
      <p>这里将展示ElasticSearch搜索功能示例</p>
      <p>功能开发中...</p>
    </t-card>
  </div>
</template>

<script setup lang="ts">
// ES搜索示例页面
</script>

<style lang="less" scoped>
.es-example-container {
  padding: 20px;
  
  h2 {
    margin-bottom: 16px;
    color: #333;
  }
  
  p {
    margin-bottom: 8px;
    color: #666;
  }
}
</style>
